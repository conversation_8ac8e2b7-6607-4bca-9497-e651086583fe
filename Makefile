# 半導體郵件處理系統 - 開發輔助命令
# 支援新的模組化 Flask 前端架構

.PHONY: help install test lint format type-check clean dev-setup quality-check all-tests
.PHONY: run-frontend run-flask run-flask-prod run-services check-structure
.PHONY: test-frontend test-backend test-integration test-e2e
.PHONY: frontend-dev backend-dev full-dev

# 變數定義
VENV = venv_win_3_11_12
PYTHON = $(VENV)/Scripts/python
PIP = $(VENV)/Scripts/pip
PYTEST = $(VENV)/Scripts/pytest
BLACK = $(VENV)/Scripts/black
MYPY = $(VENV)/Scripts/mypy
FLAKE8 = $(VENV)/Scripts/flake8
FLASK = $(VENV)/Scripts/flask

# Flask 環境變數 - 模組化架構
export FLASK_APP = frontend.app:create_app
export FLASK_ENV = development
export FLASK_DEBUG = True
export FLASK_RUN_HOST = 0.0.0.0
export FLASK_RUN_PORT = 5000
export PYTHONIOENCODING = utf-8
export PYTHONUTF8 = 1

help: ## 顯示幫助資訊
	@echo "可用命令："
	@grep -E '^[a-zA-Z_-]+:.*?## .*$$' $(MAKEFILE_LIST) | sort | awk 'BEGIN {FS = ":.*?## "}; {printf "\033[36m%-20s\033[0m %s\n", $$1, $$2}'

activate: ## 啟動虛擬環境
	@echo "🚀 啟動虛擬環境..."
	@powershell -ExecutionPolicy Bypass -File "activate_env.ps1"

# Flask 應用程式命令
run-frontend: ## 啟動前端 Flask 應用程式
	@echo "🌐 啟動前端 Flask 應用程式..."
	$(PYTHON) frontend/app.py

run-flask: ## 使用 Flask CLI 啟動應用程式
	@echo "🌐 使用 Flask CLI 啟動應用程式..."
	$(FLASK) run --host=0.0.0.0 --port=5000

run-flask-prod: ## 生產模式啟動 Flask 應用程式
	@echo "🌐 生產模式啟動 Flask 應用程式..."
	@set FLASK_ENV=production && $(FLASK) run --host=0.0.0.0 --port=5000

run-services: ## 啟動整合服務
	@echo "🚀 啟動整合服務..."
	$(PYTHON) start_integrated_services.py

install: ## 安裝開發依賴
	@echo "🔧 安裝開發依賴套件..."
	$(PIP) install -r requirements-dev.txt

test: ## 執行所有測試
	@echo "🧪 執行測試..."
	$(PYTEST)

test-unit: ## 執行單元測試
	@echo "🧪 執行單元測試..."
	$(PYTEST) tests/unit/

test-integration: ## 執行整合測試
	@echo "🧪 執行整合測試..."
	$(PYTEST) tests/integration/

test-e2e: ## 執行端對端測試
	@echo "🧪 執行端對端測試..."
	$(PYTEST) tests/e2e/

test-watch: ## 監控模式執行測試
	@echo "👀 監控模式執行測試..."
	$(PYTEST) --looponfail

format: ## 格式化程式碼
	@echo "🎨 格式化程式碼..."
	$(BLACK) src/ tests/

format-check: ## 檢查程式碼格式
	@echo "🎨 檢查程式碼格式..."
	$(BLACK) --check src/ tests/

lint: ## 執行 linting
	@echo "🔍 執行 linting..."
	$(FLAKE8) src/ tests/

type-check: ## 執行型別檢查
	@echo "🔍 執行型別檢查..."
	$(MYPY) src/

quality-check: format-check lint type-check ## 執行所有程式碼品質檢查
	@echo "✅ 程式碼品質檢查完成"

all-tests: test quality-check ## 執行所有測試和品質檢查
	@echo "✅ 所有檢查完成"

clean: ## 清理暫存檔案
	@echo "🧹 清理暫存檔案..."
	find . -type f -name "*.pyc" -delete
	find . -type d -name "__pycache__" -delete
	find . -type d -name "*.egg-info" -exec rm -rf {} +
	rm -rf .pytest_cache/
	rm -rf htmlcov/
	rm -rf .coverage
	rm -rf .mypy_cache/
	rm -rf dist/
	rm -rf build/

dev-setup: ## 設定開發環境
	@echo "🚀 設定開發環境..."
	python3 -m venv $(VENV)
	$(PIP) install --upgrade pip
	$(PIP) install -r requirements-dev.txt
	@echo "✅ 開發環境設定完成"

# TDD 工作流程命令
tdd-red: ## TDD Red 階段 - 寫失敗的測試
	@echo "🔴 TDD Red 階段 - 執行測試（預期失敗）"
	$(PYTEST) --tb=short -x || true

tdd-green: ## TDD Green 階段 - 讓測試通過
	@echo "🟢 TDD Green 階段 - 執行測試（預期通過）"
	$(PYTEST) --tb=short

tdd-refactor: ## TDD Refactor 階段 - 重構程式碼
	@echo "🔵 TDD Refactor 階段 - 重構並確保測試通過"
	$(MAKE) format
	$(MAKE) quality-check
	$(PYTEST) --tb=short

# 模組化架構專用命令
check-structure: ## 檢查前端模組化結構
	@echo "🔍 檢查前端模組化結構..."
	@if [ -d "frontend" ]; then echo "✅ frontend/ 目錄存在"; else echo "❌ frontend/ 目錄不存在"; fi
	@if [ -f "frontend/app.py" ]; then echo "✅ frontend/app.py 存在"; else echo "❌ frontend/app.py 不存在"; fi
	@for module in email analytics file_management eqc tasks monitoring shared; do \
		if [ -d "frontend/$$module" ]; then \
			echo "✅ frontend/$$module/ 模組存在"; \
		else \
			echo "❌ frontend/$$module/ 模組不存在"; \
		fi; \
	done

test-frontend: ## 執行前端模組測試
	@echo "🧪 執行前端模組測試..."
	$(PYTEST) tests/frontend/ -v

test-backend: ## 執行後端測試
	@echo "🧪 執行後端測試..."
	$(PYTEST) tests/unit/ tests/integration/ -v

test-modules: ## 按模組執行測試
	@echo "🧪 按模組執行測試..."
	@for module in email analytics file_management eqc tasks monitoring; do \
		echo "Testing $$module module..."; \
		$(PYTEST) tests/frontend/$$module/ -v || true; \
	done

frontend-dev: ## 前端開發模式（僅啟動前端）
	@echo "🎨 前端開發模式..."
	@echo "啟動模組化 Flask 前端應用程式"
	$(PYTHON) frontend/app.py

backend-dev: ## 後端開發模式（僅啟動後端服務）
	@echo "⚙️ 後端開發模式..."
	@echo "啟動後端整合服務"
	$(PYTHON) start_integrated_services.py

full-dev: ## 完整開發模式（前端+後端）
	@echo "🚀 完整開發模式..."
	@echo "同時啟動前端和後端服務"
	@echo "注意：需要在不同終端視窗中執行"
	@echo "終端1: make frontend-dev"
	@echo "終端2: make backend-dev"

# 模組化架構驗證
validate-modules: ## 驗證所有模組完整性
	@echo "🔍 驗證模組完整性..."
	@for module in email analytics file_management eqc tasks monitoring; do \
		echo "檢查 $$module 模組..."; \
		if [ -f "frontend/$$module/routes/$${module}_routes.py" ]; then \
			echo "  ✅ 路由檔案存在"; \
		else \
			echo "  ❌ 路由檔案不存在"; \
		fi; \
		if [ -d "frontend/$$module/templates" ]; then \
			echo "  ✅ 模板目錄存在"; \
		else \
			echo "  ❌ 模板目錄不存在"; \
		fi; \
		if [ -d "frontend/$$module/static" ]; then \
			echo "  ✅ 靜態資源目錄存在"; \
		else \
			echo "  ❌ 靜態資源目錄不存在"; \
		fi; \
	done

# 程式測試驗證（後端程式碼強制要求）
program-test: ## 執行程式測試驗證
	@echo "🧪 執行程式測試驗證..."
	$(PYTHON) -m src.infrastructure.config.settings --test
	@echo "✅ 程式測試驗證完成"

# 快速啟動命令
quick-start: ## 快速啟動開發環境
	@echo "⚡ 快速啟動開發環境..."
	@echo "1. 檢查結構..."
	@$(MAKE) check-structure
	@echo "2. 啟動前端應用程式..."
	$(PYTHON) frontend/app.py