# 半導體郵件處理系統 - 開發環境啟動腳本
# 支援新的模組化 Flask 前端架構
# Usage: . .\dev_env.ps1

Write-Host "🚀 半導體郵件處理系統 - 開發環境設定" -ForegroundColor Magenta
Write-Host "=" * 50 -ForegroundColor Gray

# 設定中文編碼環境變數
$env:PYTHONIOENCODING = "utf-8"
$env:PYTHONUTF8 = "1"
$env:LANG = "zh_TW.UTF-8"
Write-Host "✅ 設定中文編碼環境變數" -ForegroundColor Green

# 檢查虛擬環境
if (Test-Path ".\venv_win_3_11_12\Scripts\Activate.ps1") {
    Write-Host "✅ 啟動虛擬環境 (venv_win_3_11_12)" -ForegroundColor Green
    . ".\venv_win_3_11_12\Scripts\Activate.ps1"
    
    # 設定 Flask 環境變數 - 支援新的模組化架構
    $env:FLASK_APP = "frontend.app:create_app"
    $env:FLASK_ENV = "development"
    $env:FLASK_DEBUG = "True"
    $env:FLASK_RUN_HOST = "0.0.0.0"
    $env:FLASK_RUN_PORT = "5000"
    Write-Host "✅ 設定 Flask 環境變數 (模組化架構)" -ForegroundColor Green
    
    # 檢查前端目錄結構
    if (Test-Path ".\frontend\app.py") {
        Write-Host "✅ 檢測到新的模組化前端結構" -ForegroundColor Green
    } else {
        Write-Host "⚠️  警告：未檢測到前端模組化結構" -ForegroundColor Yellow
    }
    
    Write-Host ""
    Write-Host "🎯 開發環境就緒！可用命令：" -ForegroundColor Yellow
    Write-Host "=" * 50 -ForegroundColor Gray
    
    Write-Host "📱 前端應用程式：" -ForegroundColor Cyan
    Write-Host "  python frontend/app.py                    # 直接啟動前端應用程式" -ForegroundColor White
    Write-Host "  flask run                                 # 使用 Flask CLI 啟動" -ForegroundColor White
    Write-Host "  make run-frontend                         # 使用 Makefile 啟動前端" -ForegroundColor White
    
    Write-Host ""
    Write-Host "🔧 整合服務：" -ForegroundColor Cyan
    Write-Host "  python start_integrated_services.py      # 啟動所有後端服務" -ForegroundColor White
    Write-Host "  make run-services                         # 使用 Makefile 啟動服務" -ForegroundColor White
    
    Write-Host ""
    Write-Host "🧪 測試和品質：" -ForegroundColor Cyan
    Write-Host "  pytest                                    # 執行所有測試" -ForegroundColor White
    Write-Host "  make test                                 # 使用 Makefile 執行測試" -ForegroundColor White
    Write-Host "  make quality-check                        # 程式碼品質檢查" -ForegroundColor White
    
    Write-Host ""
    Write-Host "📚 開發工具：" -ForegroundColor Cyan
    Write-Host "  make help                                 # 查看所有可用命令" -ForegroundColor White
    Write-Host "  make dev-setup                            # 重新設定開發環境" -ForegroundColor White
    
    Write-Host ""
    Write-Host "⚙️  當前配置：" -ForegroundColor Yellow
    Write-Host "  應用程式: $env:FLASK_APP" -ForegroundColor White
    Write-Host "  環境模式: $env:FLASK_ENV" -ForegroundColor White
    Write-Host "  除錯模式: $env:FLASK_DEBUG" -ForegroundColor White
    Write-Host "  監聽地址: $env:FLASK_RUN_HOST" -ForegroundColor White
    Write-Host "  監聽埠號: $env:FLASK_RUN_PORT" -ForegroundColor White
    Write-Host "  編碼設定: UTF-8 ✅" -ForegroundColor Green
    
    Write-Host ""
    Write-Host "🌐 模組化前端架構：" -ForegroundColor Yellow
    Write-Host "  📧 Email        - /email/*" -ForegroundColor White
    Write-Host "  📊 Analytics    - /analytics/*" -ForegroundColor White
    Write-Host "  📁 Files        - /files/*" -ForegroundColor White
    Write-Host "  🔍 EQC          - /eqc/*" -ForegroundColor White
    Write-Host "  ⚡ Tasks        - /tasks/*" -ForegroundColor White
    Write-Host "  📈 Monitoring   - /monitoring/*" -ForegroundColor White
    
    Write-Host ""
    Write-Host "🚀 快速開始：執行 'python frontend/app.py' 啟動應用程式" -ForegroundColor Green
    
} else {
    Write-Host "❌ 虛擬環境未找到！" -ForegroundColor Red
    Write-Host "請執行以下命令建立虛擬環境：" -ForegroundColor Yellow
    Write-Host "  python -m venv venv_win_3_11_12" -ForegroundColor White
    Write-Host "  make dev-setup" -ForegroundColor White
}